#!/bin/bash

# 抽奖系统后端启动脚本
# 作者：AI助手
# 日期：2025-08-03

echo "=========================================="
echo "         抽奖系统后端启动脚本"
echo "=========================================="

# 检查Java环境
check_java() {
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        echo "✓ Java环境已安装: $JAVA_VERSION"
    else
        echo "✗ Java环境未安装，请先安装Java 8或更高版本"
        exit 1
    fi
}

# 检查Maven环境
check_maven() {
    if command -v mvn &> /dev/null; then
        MVN_VERSION=$(mvn -version | head -n 1)
        echo "✓ Maven环境已安装: $MVN_VERSION"
    else
        echo "✗ Maven环境未安装，请先安装Maven"
        exit 1
    fi
}

# 检查MySQL连接
check_mysql() {
    echo "检查MySQL连接..."
    # 这里可以添加MySQL连接检查逻辑
    echo "⚠ 请确保MySQL服务已启动并且数据库配置正确"
}

# 检查Redis连接
check_redis() {
    echo "检查Redis连接..."
    # 这里可以添加Redis连接检查逻辑
    echo "⚠ 请确保Redis服务已启动并且配置正确"
}

# 启动后端服务
start_backend() {
    echo "正在启动后端服务..."
    cd prize-draw-order-ruoyi/ruoyi-admin
    
    echo "执行Maven命令: mvn spring-boot:run"
    mvn spring-boot:run
}

# 主函数
main() {
    echo "开始环境检查..."
    
    check_java
    check_maven
    check_mysql
    check_redis
    
    echo ""
    echo "环境检查完成，准备启动后端服务..."
    echo "服务将在端口18080启动"
    echo ""
    
    start_backend
}

# 执行主函数
main
