# UniApp中奖记录页面查询报错问题诊断指南

## 问题现象
- UniApp中奖记录页面无法加载数据
- 控制台显示网络连接错误
- 页面显示加载状态但没有数据

## 问题原因分析

### 1. 后端服务未启动 ⭐⭐⭐⭐⭐
**最常见原因**：后端Spring Boot服务没有运行在18080端口

**检查方法**：
```bash
# 检查18080端口是否被占用
lsof -i :18080
# 或者
netstat -an | grep 18080
```

**解决方法**：
```bash
# 方法1：使用提供的启动脚本
chmod +x start-backend.sh
./start-backend.sh

# 方法2：手动启动
cd prize-draw-order-ruoyi/ruoyi-admin
mvn spring-boot:run

# 方法3：如果已编译jar包
java -jar ruoyi-admin.jar
```

### 2. 数据库连接问题 ⭐⭐⭐⭐
**原因**：MySQL数据库未启动或连接配置错误

**检查方法**：
- 查看 `prize-draw-order-ruoyi/ruoyi-admin/src/main/resources/application-druid.yml`
- 确认数据库连接信息是否正确

**解决方法**：
```bash
# 启动MySQL服务
brew services start mysql  # macOS
sudo systemctl start mysql # Linux

# 检查数据库是否存在
mysql -u root -p
USE prize_draw_order;
SHOW TABLES;
```

### 3. Redis连接问题 ⭐⭐⭐
**原因**：Redis服务未启动

**解决方法**：
```bash
# 启动Redis服务
brew services start redis  # macOS
sudo systemctl start redis # Linux

# 检查Redis连接
redis-cli ping
```

### 4. 网络配置问题 ⭐⭐
**原因**：API基础URL配置错误

**检查文件**：`prize-draw-order-uniapp/utils/api.js`
```javascript
const API_BASE_URL = "http://localhost:18080"; // 确认此地址正确
```

### 5. 跨域问题 ⭐⭐
**原因**：前端请求被浏览器跨域策略阻止

**解决方法**：
- 检查后端CORS配置
- 确认Spring Security配置允许跨域请求

## 快速诊断步骤

### 步骤1：检查后端服务状态
```bash
curl http://localhost:18080/api/health
```
如果返回404或连接拒绝，说明后端服务未启动。

### 步骤2：检查数据库连接
查看后端启动日志，寻找数据库连接错误信息。

### 步骤3：检查API调用
在浏览器开发者工具Network面板查看API请求状态。

### 步骤4：查看控制台日志
检查UniApp控制台和后端日志中的错误信息。

## 常见错误信息对照表

| 错误信息 | 可能原因 | 解决方法 |
|---------|---------|---------|
| `ECONNREFUSED` | 后端服务未启动 | 启动后端服务 |
| `request:fail` | 网络连接失败 | 检查URL和网络 |
| `timeout` | 请求超时 | 检查服务响应速度 |
| `404` | API路径错误 | 检查API路径配置 |
| `500` | 服务器内部错误 | 查看后端日志 |

## 预防措施

1. **开发环境检查清单**：
   - [ ] MySQL服务已启动
   - [ ] Redis服务已启动
   - [ ] 后端服务已启动(18080端口)
   - [ ] 数据库已初始化
   - [ ] 网络连接正常

2. **定期维护**：
   - 定期检查服务状态
   - 监控日志文件大小
   - 备份数据库数据

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 错误截图
2. 控制台日志
3. 后端启动日志
4. 系统环境信息

---
*最后更新：2025-08-03*
