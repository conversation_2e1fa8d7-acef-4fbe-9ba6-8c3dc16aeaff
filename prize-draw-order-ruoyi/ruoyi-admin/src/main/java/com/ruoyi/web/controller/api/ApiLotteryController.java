package com.ruoyi.web.controller.api;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.LotteryActivity;
import com.ruoyi.system.domain.LotteryRecord;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.domain.dto.DrawLotteryRequest;
import com.ruoyi.system.service.ILotteryActivityService;
import com.ruoyi.system.service.ILotteryRecordService;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantTableService;

/**
 * 抽奖API接口 - 小程序端调用
 *
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/api/lottery")
public class ApiLotteryController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantTableService merchantTableService;

    @Autowired
    private ILotteryActivityService lotteryActivityService;

    @Autowired
    private ILotteryRecordService lotteryRecordService;

    /**
     * 根据商家编码获取当前有效的抽奖活动
     */
    @GetMapping("/activity/{merchantCode}")
    public AjaxResult getCurrentActivity(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        LotteryActivity activity = lotteryActivityService.selectCurrentValidActivity(merchant.getMerchantId());
        if (activity == null)
        {
            return error("暂无进行中的抽奖活动");
        }
        
        return success(activity);
    }

    /**
     * 根据活动ID获取活动详情
     */
    @GetMapping("/activity/detail/{activityId}")
    public AjaxResult getActivityDetail(@PathVariable Long activityId)
    {
        LotteryActivity activity = lotteryActivityService.selectLotteryActivityById(activityId);
        if (activity == null)
        {
            return error("活动不存在");
        }

        return success(activity);
    }

    /**
     * 根据商家编码获取所有有效的抽奖活动
     */
    @GetMapping("/activities/{merchantCode}")
    public AjaxResult getValidActivities(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        List<LotteryActivity> activities = lotteryActivityService.selectValidLotteryActivities(merchant.getMerchantId());
        return success(activities);
    }

    /**
     * 检查用户是否可以参与抽奖
     */
    @GetMapping("/check/{activityId}/{userOpenid}")
    public AjaxResult checkParticipate(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        boolean canParticipate = lotteryActivityService.canUserParticipate(activityId, userOpenid);
        if (canParticipate) {
            int remaining = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);
            return AjaxResult.success("可以参与抽奖", remaining);
        } else {
            return error("您今日的抽奖次数已用完或活动已结束");
        }
    }

    /**
     * 获取用户剩余抽奖次数
     */
    @GetMapping("/remaining/{activityId}/{userOpenid}")
    public AjaxResult getRemainingDraws(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int remaining = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);
        return success(remaining);
    }

    /**
     * 获取用户抽奖次数详情
     */
    @GetMapping("/draws-info/{activityId}/{userOpenid}")
    public AjaxResult getDrawsInfo(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        try
        {
            // 检查活动是否存在
            LotteryActivity activity = lotteryActivityService.selectLotteryActivityById(activityId);
            if (activity == null)
            {
                return error("活动不存在");
            }

            // 获取用户抽奖统计信息
            int todayDraws = lotteryRecordService.countUserTodayDraws(activityId, userOpenid);
            int totalDraws = lotteryRecordService.countUserTotalDraws(activityId, userOpenid);
            int remainingDraws = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);

            Map<String, Object> result = new HashMap<>();
            result.put("dailyLimit", activity.getDailyLimit());
            result.put("totalLimit", activity.getTotalLimit());
            result.put("todayDraws", todayDraws);
            result.put("totalDraws", totalDraws);
            result.put("remainingDraws", remainingDraws);

            // 计算今日剩余次数
            int dailyRemaining = 0;
            if (activity.getDailyLimit() != null && activity.getDailyLimit() > 0)
            {
                dailyRemaining = Math.max(0, activity.getDailyLimit() - todayDraws);
            }
            result.put("dailyRemaining", dailyRemaining);

            // 计算总剩余次数
            int totalRemaining = 0;
            if (activity.getTotalLimit() != null && activity.getTotalLimit() > 0)
            {
                totalRemaining = Math.max(0, activity.getTotalLimit() - totalDraws);
            }
            result.put("totalRemaining", totalRemaining);

            return success(result);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 执行抽奖
     */
    @PostMapping("/draw")
    public AjaxResult performDraw(@Valid @RequestBody DrawLotteryRequest request,
                                HttpServletRequest httpRequest)
    {
        try
        {
            // 验证桌台信息（如果提供了桌台ID）
            if (request.getTableId() != null)
            {
                MerchantTable table = merchantTableService.selectMerchantTableById(request.getTableId());
                if (table == null || !"0".equals(table.getStatus()))
                {
                    return error("桌台信息无效");
                }
            }

            String drawIp = IpUtils.getIpAddr(httpRequest);
            LotteryRecord record = lotteryRecordService.performDraw(request.getActivityId(), request.getUserOpenid(),
                request.getUserNickname(), request.getUserAvatar(), request.getTableId(), drawIp);

            return AjaxResult.success("抽奖成功", record);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 获取用户抽奖记录
     */
    @GetMapping("/records/{userOpenid}")
    public AjaxResult getUserRecords(@PathVariable String userOpenid)
    {
        List<LotteryRecord> records = lotteryRecordService.selectLotteryRecordsByUserOpenid(userOpenid);
        return success(records);
    }

    /**
     * 根据商家编码获取用户抽奖记录
     */
    @GetMapping("/records/{merchantCode}/{userOpenid}")
    public AjaxResult getUserRecordsByMerchant(@PathVariable String merchantCode, @PathVariable String userOpenid)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }

        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }

        List<LotteryRecord> records = lotteryRecordService.selectLotteryRecordsByMerchantId(merchant.getMerchantId());
        // 过滤出指定用户的记录
        List<LotteryRecord> userRecords = records.stream()
            .filter(record -> userOpenid.equals(record.getUserOpenid()))
            .collect(Collectors.toList());

        return success(userRecords);
    }

    /**
     * 根据商家编码获取用户中奖记录
     */
    @GetMapping("/winning/{merchantCode}/{userOpenid}")
    public AjaxResult getUserWinningRecordsByMerchant(@PathVariable String merchantCode, @PathVariable String userOpenid)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }

        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }

        List<LotteryRecord> records = lotteryRecordService.selectLotteryRecordsByMerchantId(merchant.getMerchantId());
        // 过滤出指定用户的中奖记录
        List<LotteryRecord> userWinningRecords = records.stream()
            .filter(record -> userOpenid.equals(record.getUserOpenid()) && "1".equals(record.getIsWinner()))
            .collect(Collectors.toList());

        return success(userWinningRecords);
    }

    /**
     * 根据商家编码获取用户未领取的中奖记录
     */
    @GetMapping("/unclaimed/{merchantCode}/{userOpenid}")
    public AjaxResult getUserUnclaimedRecordsByMerchant(@PathVariable String merchantCode, @PathVariable String userOpenid)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }

        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }

        List<LotteryRecord> records = lotteryRecordService.selectLotteryRecordsByMerchantId(merchant.getMerchantId());
        // 过滤出指定用户的未领取中奖记录
        List<LotteryRecord> userUnclaimedRecords = records.stream()
            .filter(record -> userOpenid.equals(record.getUserOpenid())
                && "1".equals(record.getIsWinner())
                && "0".equals(record.getClaimStatus()))
            .collect(Collectors.toList());

        return success(userUnclaimedRecords);
    }

    /**
     * 获取用户在指定活动的抽奖记录
     */
    @GetMapping("/records/{activityId}/{userOpenid}")
    public AjaxResult getUserActivityRecords(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        List<LotteryRecord> records = lotteryRecordService.selectUserActivityRecords(activityId, userOpenid);
        return success(records);
    }

    /**
     * 获取用户中奖记录
     */
    @GetMapping("/winning/{userOpenid}")
    public AjaxResult getUserWinningRecords(@PathVariable String userOpenid)
    {
        LotteryRecord queryRecord = new LotteryRecord();
        queryRecord.setUserOpenid(userOpenid);
        List<LotteryRecord> records = lotteryRecordService.selectWinningRecords(queryRecord);
        return success(records);
    }

    /**
     * 获取用户未领取的中奖记录
     */
    @GetMapping("/unclaimed/{userOpenid}")
    public AjaxResult getUserUnclaimedRecords(@PathVariable String userOpenid)
    {
        LotteryRecord queryRecord = new LotteryRecord();
        queryRecord.setUserOpenid(userOpenid);
        List<LotteryRecord> records = lotteryRecordService.selectUnclaimedWinningRecords(queryRecord);
        return success(records);
    }

    /**
     * 查询用户今日抽奖次数
     */
    @GetMapping("/count/today/{activityId}/{userOpenid}")
    public AjaxResult getTodayDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTodayDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 查询用户总抽奖次数
     */
    @GetMapping("/count/total/{activityId}/{userOpenid}")
    public AjaxResult getTotalDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTotalDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 根据商家编码和桌台号获取抽奖信息
     */
    @GetMapping("/info/{merchantCode}/{tableNumber}")
    public AjaxResult getLotteryInfo(@PathVariable String merchantCode, @PathVariable String tableNumber)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        // 获取桌台信息
        MerchantTable table = merchantTableService.selectMerchantTableByMerchantIdAndNumber(
            merchant.getMerchantId(), tableNumber);
        if (table == null || !"0".equals(table.getStatus()))
        {
            return error("桌台信息无效");
        }
        
        // 获取当前有效的抽奖活动
        LotteryActivity activity = lotteryActivityService.selectCurrentValidActivity(merchant.getMerchantId());
        if (activity == null)
        {
            return error("暂无进行中的抽奖活动");
        }
        
        // 组装返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("merchant", merchant);
        result.put("table", table);
        result.put("activity", activity);
        
        return success(result);
    }

    /**
     * 获取用户在指定活动中的抽奖状态
     * 返回用户的抽奖状态和最新的抽奖记录
     */
    @GetMapping("/status/{activityId}/{userOpenid}")
    public AjaxResult getUserLotteryStatus(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        try
        {
            // 检查活动是否存在
            LotteryActivity activity = lotteryActivityService.selectLotteryActivityById(activityId);
            if (activity == null)
            {
                return error("活动不存在");
            }

            // 获取用户在该活动中的抽奖记录
            List<LotteryRecord> records = lotteryRecordService.selectUserActivityRecords(activityId, userOpenid);

            Map<String, Object> result = new HashMap<>();

            // 获取剩余抽奖次数
            int remainingDraws = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);
            boolean canDraw = lotteryActivityService.canUserParticipate(activityId, userOpenid);

            result.put("hasDrawn", !records.isEmpty());
            result.put("canDraw", canDraw);
            result.put("remainingDraws", remainingDraws);

            // 如果有抽奖记录，返回最新的一条记录
            if (!records.isEmpty())
            {
                // 按抽奖时间倒序排列，取最新的一条
                LotteryRecord latestRecord = records.stream()
                    .max((r1, r2) -> r1.getDrawTime().compareTo(r2.getDrawTime()))
                    .orElse(records.get(0));
                result.put("lotteryRecord", latestRecord);
            }

            return success(result);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 获取抽奖记录详情
     */
    @GetMapping("/record/{recordId}")
    public AjaxResult getRecordDetail(@PathVariable Long recordId)
    {
        LotteryRecord record = lotteryRecordService.selectLotteryRecordById(recordId);
        if (record == null)
        {
            return error("记录不存在");
        }

        return success(record);
    }

    /**
     * 标记奖品为已领取
     */
    @PostMapping("/claim/{recordId}")
    public AjaxResult markAsClaimed(@PathVariable Long recordId)
    {
        try
        {
            LotteryRecord record = lotteryRecordService.selectLotteryRecordById(recordId);
            if (record == null)
            {
                return error("记录不存在");
            }

            if (!"1".equals(record.getIsWinner()))
            {
                return error("该记录不是中奖记录");
            }

            if ("1".equals(record.getClaimStatus()))
            {
                return error("奖品已经领取过了");
            }

            // 更新领取状态
            record.setClaimStatus("1");
            record.setClaimTime(new Date());
            lotteryRecordService.updateLotteryRecord(record);

            return success("领取成功");
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }
}
