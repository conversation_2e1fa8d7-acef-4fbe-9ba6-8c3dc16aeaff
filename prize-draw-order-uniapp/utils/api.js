// API配置
const API_BASE_URL = "http://localhost:18080"; // 后台服务地址，需要根据实际情况修改

// 图片URL处理函数
export const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return "";
  }

  // 如果已经是完整的URL（包含http或https），直接返回
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }

  // 如果是相对路径，拼接基础URL
  // 确保路径以/开头
  const path = imagePath.startsWith("/") ? imagePath : "/" + imagePath;
  return API_BASE_URL + path;
};

// 请求封装（增强版，包含重试机制和详细错误处理）
const request = (options, retryCount = 0) => {
  const maxRetries = options.maxRetries || 2;
  const timeout = options.timeout || 10000;

  return new Promise((resolve, reject) => {
    console.log(
      `发起请求: ${options.method || "GET"} ${API_BASE_URL + options.url}`,
      options.data
    );

    uni.request({
      url: API_BASE_URL + options.url,
      method: options.method || "GET",
      data: options.data || {},
      timeout: timeout,
      header: {
        "Content-Type": "application/json",
        ...options.header,
      },
      success: (res) => {
        console.log(`请求成功: ${options.url}`, res);

        if (res.statusCode === 200) {
          if (res.data && res.data.code === 200) {
            resolve(res.data);
          } else {
            const errorMsg = res.data?.msg || "请求失败";
            console.error(`业务错误: ${options.url}`, res.data);

            // 只在非静默模式下显示错误提示
            if (!options.silent) {
              uni.showToast({
                title: errorMsg,
                icon: "none",
                duration: 3000,
              });
            }
            reject(res.data);
          }
        } else {
          console.error(`HTTP错误: ${options.url}`, res);
          const errorMsg = `请求失败 (${res.statusCode})`;

          if (!options.silent) {
            uni.showToast({
              title: errorMsg,
              icon: "none",
              duration: 3000,
            });
          }
          reject(res);
        }
      },
      fail: (err) => {
        console.error(`请求失败: ${options.url}`, err);

        // 检查是否需要重试
        if (retryCount < maxRetries && shouldRetry(err)) {
          console.log(
            `重试请求 (${retryCount + 1}/${maxRetries}): ${options.url}`
          );
          setTimeout(() => {
            request(options, retryCount + 1)
              .then(resolve)
              .catch(reject);
          }, 1000 * (retryCount + 1)); // 递增延迟
          return;
        }

        // 根据错误类型显示不同的提示信息
        let errorMsg = "网络连接失败";
        if (err.errMsg) {
          if (err.errMsg.includes("timeout")) {
            errorMsg = "请求超时，请检查网络连接";
          } else if (err.errMsg.includes("fail")) {
            errorMsg = "无法连接到服务器，请检查后端服务是否启动";
          }
        }

        if (!options.silent) {
          uni.showToast({
            title: errorMsg,
            icon: "none",
            duration: 5000,
          });
        }

        reject({
          ...err,
          isNetworkError: true,
          userMessage: errorMsg,
        });
      },
    });
  });
};

// 判断是否应该重试
const shouldRetry = (error) => {
  // 网络错误或超时错误可以重试
  if (error.errMsg) {
    return (
      error.errMsg.includes("timeout") ||
      error.errMsg.includes("fail") ||
      error.errMsg.includes("abort")
    );
  }
  return false;
};

// 商家相关API
export const merchantApi = {
  // 获取商家信息
  getMerchantInfo(merchantCode) {
    return request({
      url: `/api/merchant/info/${merchantCode}`,
    });
  },

  // 获取商家配置
  getMerchantConfig(merchantCode) {
    return request({
      url: `/api/merchant/config/${merchantCode}`,
    });
  },

  // 检查商家状态
  checkMerchantStatus(merchantCode) {
    return request({
      url: `/api/merchant/check/${merchantCode}`,
    });
  },
};

// 桌台相关API
export const tableApi = {
  // 获取桌台信息
  getTableInfo(merchantCode, tableNumber) {
    return request({
      url: `/api/table/${merchantCode}/${tableNumber}`,
    });
  },

  // 获取桌台列表
  getTableList(merchantCode) {
    return request({
      url: `/api/table/list/${merchantCode}`,
    });
  },

  // 根据桌台ID获取信息
  getTableById(tableId) {
    return request({
      url: `/api/table/info/${tableId}`,
    });
  },
};

// 抽奖相关API
export const lotteryApi = {
  // 获取当前有效活动
  getCurrentActivity(merchantCode) {
    return request({
      url: `/api/lottery/activity/${merchantCode}`,
    });
  },

  // 获取所有有效活动
  getValidActivities(merchantCode) {
    return request({
      url: `/api/lottery/activities/${merchantCode}`,
    });
  },

  // 获取单个活动信息
  getLotteryActivity(activityId) {
    return request({
      url: `/api/lottery/activity/detail/${activityId}`,
    });
  },

  // 检查用户是否可以参与抽奖
  checkParticipate(activityId, userOpenid) {
    return request({
      url: `/api/lottery/check/${activityId}/${userOpenid}`,
    });
  },

  // 获取用户剩余抽奖次数
  getRemainingDraws(activityId, userOpenid) {
    return request({
      url: `/api/lottery/remaining/${activityId}/${userOpenid}`,
    });
  },

  // 获取用户抽奖次数详情
  getDrawsInfo(activityId, userOpenid) {
    return request({
      url: `/api/lottery/draws-info/${activityId}/${userOpenid}`,
    });
  },

  // 执行抽奖
  performDraw(data) {
    return request({
      url: "/api/lottery/draw",
      method: "POST",
      data: data,
    });
  },

  // 获取用户抽奖记录
  getUserRecords(userOpenid) {
    return request({
      url: `/api/lottery/records/${userOpenid}`,
    });
  },

  // 根据商家编码获取用户抽奖记录
  getUserRecordsByMerchant(merchantCode, userOpenid) {
    return request({
      url: `/api/lottery/records/${merchantCode}/${userOpenid}`,
    });
  },

  // 获取用户在指定活动的抽奖记录
  getUserActivityRecords(activityId, userOpenid) {
    return request({
      url: `/api/lottery/records/activity/${activityId}/${userOpenid}`,
    });
  },

  // 获取用户中奖记录
  getUserWinningRecords(userOpenid) {
    return request({
      url: `/api/lottery/winning/${userOpenid}`,
    });
  },

  // 根据商家编码获取用户中奖记录
  getUserWinningRecordsByMerchant(merchantCode, userOpenid) {
    return request({
      url: `/api/lottery/winning/${merchantCode}/${userOpenid}`,
    });
  },

  // 获取用户未领取的中奖记录
  getUserUnclaimedRecords(userOpenid) {
    return request({
      url: `/api/lottery/unclaimed/${userOpenid}`,
    });
  },

  // 根据商家编码获取用户未领取的中奖记录
  getUserUnclaimedRecordsByMerchant(merchantCode, userOpenid) {
    return request({
      url: `/api/lottery/unclaimed/${merchantCode}/${userOpenid}`,
    });
  },

  // 查询用户今日抽奖次数
  getTodayDrawCount(activityId, userOpenid) {
    return request({
      url: `/api/lottery/count/today/${activityId}/${userOpenid}`,
    });
  },

  // 查询用户总抽奖次数
  getTotalDrawCount(activityId, userOpenid) {
    return request({
      url: `/api/lottery/count/total/${activityId}/${userOpenid}`,
    });
  },

  // 根据商家编码和桌台号获取抽奖信息
  getLotteryInfo(merchantCode, tableNumber) {
    return request({
      url: `/api/lottery/info/${merchantCode}/${tableNumber}`,
    });
  },

  // 获取用户在指定活动中的抽奖状态
  getUserLotteryStatus(activityId, userOpenid) {
    return request({
      url: `/api/lottery/status/${activityId}/${userOpenid}`,
    });
  },

  // 获取抽奖记录详情
  getRecordDetail(recordId) {
    return request({
      url: `/api/lottery/record/${recordId}`,
    });
  },

  // 标记奖品为已领取
  markAsClaimed(recordId) {
    return request({
      url: `/api/lottery/claim/${recordId}`,
      method: "POST",
    });
  },
};

// 配置相关API
export const configApi = {
  // 获取所有配置
  getAllConfig(merchantCode) {
    return request({
      url: `/api/config/${merchantCode}`,
    });
  },

  // 获取领取说明
  getClaimInstruction(merchantCode) {
    return request({
      url: `/api/config/claim-instruction/${merchantCode}`,
    });
  },

  // 获取扫码页面背景图片
  getScanPageBg(merchantCode) {
    return request({
      url: `/api/config/scan-bg/${merchantCode}`,
    });
  },

  // 获取抽奖页面背景图片
  getLotteryBg(merchantCode) {
    return request({
      url: `/api/config/lottery-bg/${merchantCode}`,
    });
  },

  // 根据配置键获取配置值
  getConfigValue(merchantCode, configKey) {
    return request({
      url: `/api/config/${merchantCode}/${configKey}`,
    });
  },
};

// 系统健康检查API
export const healthApi = {
  // 检查服务器健康状态
  checkHealth() {
    return request({
      url: "/api/health",
      silent: true, // 静默模式，不显示错误提示
      timeout: 5000,
      maxRetries: 0, // 健康检查不重试
    });
  },

  // 检查数据库连接
  checkDatabase() {
    return request({
      url: "/api/health/database",
      silent: true,
      timeout: 5000,
      maxRetries: 0,
    });
  },

  // 检查Redis连接
  checkRedis() {
    return request({
      url: "/api/health/redis",
      silent: true,
      timeout: 5000,
      maxRetries: 0,
    });
  },
};

export default {
  merchantApi,
  tableApi,
  lotteryApi,
  configApi,
  healthApi,
};
