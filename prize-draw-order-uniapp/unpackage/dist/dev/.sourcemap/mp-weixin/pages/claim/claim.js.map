{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?575a", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?7ea6", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?2581", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?4b98", "uni-app:///pages/claim/claim.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?fa7f", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/claim/claim.vue?1810"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordId", "recordDetail", "activityInfo", "claimInstruction", "isLoading", "merchantCode", "merchantConfig", "imageLoadError", "computed", "uiConfig", "console", "primaryColor", "backgroundGradient", "watch", "handler", "uni", "frontColor", "backgroundColor", "immediate", "onLoad", "methods", "initPage", "loadMerchantInfo", "merchantApi", "res", "loadMerchantConfig", "config<PERSON>pi", "loadRecordDetail", "lotteryApi", "title", "icon", "loadActivityInfo", "<PERSON><PERSON><PERSON>laimed", "goBack", "url", "formatTime", "getFullImageUrl", "handleImageError", "handleImageLoad", "adjustColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0Ev1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAF;MACAG;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAX;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAGAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAF;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAJ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAd;gBACAK;kBACAc;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAH;cAAA;gBAAAJ;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAJ;cAAA;gBAAAJ;gBACA;kBACA;kBACA;kBACAT;oBACAc;oBACAC;kBACA;gBACA;kBACAf;oBACAc;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApB;gBACAK;kBACAc;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAG;MACA;MACA;QACAlB;MACA;QACAA;UACAmB;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA1B;MACAA;MACA;IACA;IAEA;IACA2B;MACA3B;MACAA;MACA;IACA;IAEA;IACA4B;MACA5B;MACA;IACA;IAEA;IACA6B;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvSA;AAAA;AAAA;AAAA;AAA0jD,CAAgB,07CAAG,EAAC,C;;;;;;;;;;;ACA9kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/claim/claim.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/claim/claim.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./claim.vue?vue&type=template&id=011e7c40&scoped=true&\"\nvar renderjs\nimport script from \"./claim.vue?vue&type=script&lang=js&\"\nexport * from \"./claim.vue?vue&type=script&lang=js&\"\nimport style0 from \"./claim.vue?vue&type=style&index=0&id=011e7c40&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"011e7c40\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/claim/claim.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./claim.vue?vue&type=template&id=011e7c40&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.isLoading && _vm.recordDetail\n      ? _vm.formatTime(_vm.recordDetail.drawTime)\n      : null\n  var m1 =\n    !_vm.isLoading &&\n    _vm.recordDetail &&\n    _vm.activityInfo &&\n    _vm.activityInfo.wechatQrcode\n      ? _vm.getFullImageUrl(_vm.activityInfo.wechatQrcode)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./claim.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./claim.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"claim-container\" :style=\"'background:' + backgroundGradient\">\n        <!-- 加载状态 -->\n        <view class=\"loading-container\" v-if=\"isLoading\">\n            <view class=\"loading-icon\">⏳</view>\n            <view class=\"loading-text\">加载中...</view>\n        </view>\n\n        <!-- 中奖信息 -->\n        <view class=\"claim-content\" v-if=\"!isLoading && recordDetail\">\n            <!-- 奖品信息 -->\n            <view class=\"prize-section\">\n                <view class=\"prize-icon\">🎁</view>\n                <view class=\"prize-title\">恭喜您获得</view>\n                <view class=\"prize-name\">{{ recordDetail.prizeName }}</view>\n                <view class=\"prize-desc\" v-if=\"recordDetail.prizeValue\">{{ recordDetail.prizeValue }}</view>\n            </view>\n\n            <!-- 中奖记录信息 -->\n            <view class=\"record-info\">\n                <view class=\"info-item\">\n                    <text class=\"label\">中奖时间：</text>\n                    <text class=\"value\">{{ formatTime(recordDetail.drawTime) }}</text>\n                </view>\n                <view class=\"info-item\">\n                    <text class=\"label\">记录编号：</text>\n                    <text class=\"value\">{{ recordDetail.recordId }}</text>\n                </view>\n                <view class=\"info-item\">\n                    <text class=\"label\">领取状态：</text>\n                    <text class=\"value status\" :class=\"{ 'claimed': recordDetail.claimStatus === '1' }\">\n                        {{ recordDetail.claimStatus === '1' ? '已领取' : '待领取' }}\n                    </text>\n                </view>\n            </view>\n\n            <!-- 领取说明 -->\n            <view class=\"claim-instruction\" v-if=\"claimInstruction\">\n                <view class=\"instruction-title\">领取说明</view>\n                <view class=\"instruction-content\">{{ claimInstruction }}</view>\n            </view>\n\n            <!-- 微信二维码 -->\n            <view class=\"wechat-qrcode\" v-if=\"activityInfo && activityInfo.wechatQrcode\">\n                <image :src=\"getFullImageUrl(activityInfo.wechatQrcode)\" class=\"qrcode-img\" mode=\"aspectFit\"\n                    @error=\"handleImageError\" @load=\"handleImageLoad\"></image>\n                <view class=\"qrcode-error\" v-if=\"imageLoadError\">\n                    <text>二维码加载失败</text>\n                </view>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"action-buttons\">\n                <view class=\"btn btn-secondary\" @click=\"goBack\">\n                    <text>返回</text>\n                </view>\n                <view class=\"btn btn-primary\" @click=\"markAsClaimed\" v-if=\"recordDetail.claimStatus === '0'\">\n                    <text>确认领取</text>\n                </view>\n            </view>\n        </view>\n\n        <!-- 错误状态 -->\n        <view class=\"error-container\" v-if=\"!isLoading && !recordDetail\">\n            <view class=\"error-icon\">❌</view>\n            <view class=\"error-text\">记录不存在或已过期</view>\n            <view class=\"btn btn-primary\" @click=\"goBack\">\n                <text>返回</text>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport { lotteryApi, configApi, getImageUrl, merchantApi } from '@/utils/api.js'\n\nexport default {\n    data() {\n        return {\n            recordId: '',\n            recordDetail: null,\n            activityInfo: null,\n            claimInstruction: '',\n            isLoading: true,\n            merchantCode: '',\n            merchantConfig: {},\n            imageLoadError: false\n        }\n    },\n\n    computed: {\n        // 解析UI配置\n        uiConfig() {\n            const uiConfigStr = this.merchantConfig.ui_config\n            if (uiConfigStr) {\n                try {\n                    return JSON.parse(uiConfigStr)\n                } catch (e) {\n                    console.error('UI配置解析失败:', e)\n                    return {}\n                }\n            }\n            return {}\n        },\n\n        // 主题色彩\n        primaryColor() {\n            return this.uiConfig.primaryColor || '#667eea'\n        },\n\n        // 背景渐变色\n        backgroundGradient() {\n            const color = this.primaryColor\n            // 生成基于主题色的渐变背景\n            return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`\n        }\n    },\n\n    watch: {\n        // 监听主题色彩变化，动态设置导航栏颜色\n        primaryColor: {\n            handler(newColor) {\n                if (newColor) {\n                    uni.setNavigationBarColor({\n                        frontColor: '#ffffff',\n                        backgroundColor: newColor\n                    })\n                }\n            },\n            immediate: true\n        }\n    },\n\n    onLoad(options) {\n        this.recordId = options.recordId || ''\n        this.initPage()\n    },\n\n    methods: {\n        async initPage() {\n            try {\n                if (this.recordId) {\n                    //加载活动信息\n                    await this.loadRecordDetail();\n                    this.merchantCode = this.recordDetail.merchantCode\n                    // 加载商家信息\n                    await this.loadMerchantInfo()\n                    // 加载商家配置\n                    await this.loadMerchantConfig()\n                } else {\n                    this.isLoading = false\n                }\n            } catch (error) {\n                console.error('页面初始化失败:', error)\n                this.isLoading = false\n            }\n        },\n\n\n        async loadMerchantInfo() {\n            try {\n                const res = await merchantApi.getMerchantInfo(this.merchantCode)\n                if (res.code === 200) {\n                    this.merchantInfo = res.data\n                } else {\n                    throw new Error(res.msg || '获取商家信息失败')\n                }\n            } catch (error) {\n                this.handleError(error)\n            }\n        },\n        async loadMerchantConfig() {\n            try {\n                const res = await configApi.getAllConfig(this.merchantCode)\n                if (res.code === 200) {\n                    this.merchantConfig = res.data || {}\n                }\n            } catch (error) {\n                console.error('获取商家配置失败:', error)\n            }\n        },\n        async loadRecordDetail() {\n            try {\n                const res = await lotteryApi.getRecordDetail(this.recordId)\n                if (res.code === 200) {\n                    this.recordDetail = res.data\n                    // 加载活动信息\n                    if (this.recordDetail.activityId) {\n                        await this.loadActivityInfo()\n                    }\n                }\n            } catch (error) {\n                console.error('获取记录详情失败:', error)\n                uni.showToast({\n                    title: '获取详情失败',\n                    icon: 'none'\n                })\n            } finally {\n                this.isLoading = false\n            }\n        },\n\n        async loadActivityInfo() {\n            try {\n                const res = await lotteryApi.getLotteryActivity(this.recordDetail.activityId)\n                if (res.code === 200 && res.data) {\n                    this.activityInfo = res.data\n                    this.claimInstruction = res.data.claimInstruction || '请到前台出示此页面领取奖品'\n                }\n            } catch (error) {\n                console.error('获取活动信息失败:', error)\n                this.claimInstruction = '请到前台出示此页面领取奖品'\n            }\n        },\n\n        async markAsClaimed() {\n            try {\n                const res = await lotteryApi.markAsClaimed(this.recordId)\n                if (res.code === 200) {\n                    this.recordDetail.claimStatus = '1'\n                    this.recordDetail.claimTime = new Date().toISOString()\n                    uni.showToast({\n                        title: '领取成功',\n                        icon: 'success'\n                    })\n                } else {\n                    uni.showToast({\n                        title: res.msg || '操作失败',\n                        icon: 'none'\n                    })\n                }\n            } catch (error) {\n                console.error('标记领取失败:', error)\n                uni.showToast({\n                    title: '操作失败',\n                    icon: 'none'\n                })\n            }\n        },\n\n        goBack() {\n            const pages = getCurrentPages()\n            if (pages.length > 1) {\n                uni.navigateBack()\n            } else {\n                uni.redirectTo({\n                    url: '/pages/lottery/lottery'\n                })\n            }\n        },\n\n        formatTime(timeStr) {\n            if (!timeStr) return ''\n            const date = new Date(timeStr)\n            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\n        },\n\n        // 获取完整的图片URL\n        getFullImageUrl(imagePath) {\n            const fullUrl = getImageUrl(imagePath)\n            console.log('Claim页面 - 原始图片路径:', imagePath)\n            console.log('Claim页面 - 处理后的图片URL:', fullUrl)\n            return fullUrl\n        },\n\n        // 图片加载错误处理\n        handleImageError(event) {\n            console.error('Claim页面 - 微信二维码图片加载失败:', event)\n            console.error('Claim页面 - 图片URL:', this.getFullImageUrl(this.activityInfo.wechatQrcode))\n            this.imageLoadError = true\n        },\n\n        // 图片加载成功处理\n        handleImageLoad(event) {\n            console.log('Claim页面 - 微信二维码图片加载成功:', event)\n            this.imageLoadError = false\n        },\n\n        // 颜色调整工具方法\n        adjustColor(color, amount) {\n            // 将十六进制颜色转换为RGB\n            const hex = color.replace('#', '')\n            const r = parseInt(hex.substr(0, 2), 16)\n            const g = parseInt(hex.substr(2, 2), 16)\n            const b = parseInt(hex.substr(4, 2), 16)\n\n            // 调整亮度\n            const newR = Math.max(0, Math.min(255, r + amount))\n            const newG = Math.max(0, Math.min(255, g + amount))\n            const newB = Math.max(0, Math.min(255, b + amount))\n\n            // 转换回十六进制\n            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.claim-container {\n    min-height: 100vh;\n    /* 背景色通过内联样式动态设置 */\n    padding: 40rpx;\n}\n\n.loading-container,\n.error-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    min-height: 60vh;\n    text-align: center;\n}\n\n.loading-icon,\n.error-icon {\n    font-size: 80rpx;\n    margin-bottom: 30rpx;\n}\n\n.loading-text,\n.error-text {\n    font-size: 32rpx;\n    color: rgba(255, 255, 255, 0.8);\n    margin-bottom: 40rpx;\n}\n\n.claim-content {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 20rpx;\n    padding: 40rpx;\n    margin-bottom: 40rpx;\n}\n\n.prize-section {\n    text-align: center;\n    margin-bottom: 40rpx;\n    padding-bottom: 40rpx;\n    border-bottom: 1px solid #eee;\n}\n\n.prize-icon {\n    font-size: 80rpx;\n    margin-bottom: 20rpx;\n}\n\n.prize-title {\n    font-size: 28rpx;\n    color: #666;\n    margin-bottom: 10rpx;\n}\n\n.prize-name {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 10rpx;\n}\n\n.prize-desc {\n    font-size: 28rpx;\n    color: #999;\n}\n\n.record-info {\n    margin-bottom: 40rpx;\n}\n\n.info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx 0;\n    border-bottom: 1px solid #f5f5f5;\n}\n\n.label {\n    font-size: 28rpx;\n    color: #666;\n}\n\n.value {\n    font-size: 28rpx;\n    color: #333;\n}\n\n.status.claimed {\n    color: #52c41a;\n}\n\n.claim-instruction {\n    margin-bottom: 40rpx;\n    padding: 30rpx;\n    background: #f8f9fa;\n    border-radius: 10rpx;\n}\n\n.instruction-title {\n    font-size: 30rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n}\n\n.instruction-content {\n    font-size: 28rpx;\n    color: #666;\n    line-height: 1.6;\n}\n\n.wechat-qrcode {\n    text-align: center;\n    margin-bottom: 40rpx;\n}\n\n.qrcode-title {\n    font-size: 28rpx;\n    color: #666;\n    margin-bottom: 20rpx;\n}\n\n.qrcode-img {\n    width: 200rpx;\n    height: 200rpx;\n    border-radius: 10rpx;\n    margin-bottom: 15rpx;\n}\n\n.qrcode-desc {\n    color: #666;\n    font-size: 24rpx;\n    line-height: 1.4;\n}\n\n.qrcode-error {\n    color: #ff4757;\n    font-size: 24rpx;\n    padding: 20rpx;\n    background: rgba(255, 71, 87, 0.1);\n    border-radius: 8rpx;\n    margin-top: 15rpx;\n}\n\n.action-buttons {\n    display: flex;\n    gap: 20rpx;\n}\n\n.btn {\n    flex: 1;\n    height: 80rpx;\n    border-radius: 40rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 30rpx;\n    font-weight: bold;\n}\n\n.btn-primary {\n    background: linear-gradient(135deg, #667eea, #764ba2);\n    color: white;\n}\n\n.btn-secondary {\n    background: #f5f5f5;\n    color: #666;\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./claim.vue?vue&type=style&index=0&id=011e7c40&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./claim.vue?vue&type=style&index=0&id=011e7c40&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754185599729\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}