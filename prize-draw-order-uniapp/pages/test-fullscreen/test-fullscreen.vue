<template>
  <view class="test-container">
    <view class="test-header">
      <text class="title">中奖页面全屏测试</text>
    </view>

    <view class="test-buttons">
      <button class="test-btn" @click="showWinningModal">测试中奖弹窗</button>
      <button class="test-btn" @click="showLastWinning">测试上次中奖记录</button>
    </view>

    <!-- 中奖弹窗组件 -->
    <WinningModal
      :visible="winningModalVisible"
      :result="testResult"
      :merchantConfig="testMerchantConfig"
      :activityInfo="testActivity"
      @close="closeWinningModal"
    />

    <!-- 上次中奖记录全屏显示 -->
    <view class="last-winning-fullscreen" v-if="lastWinningVisible" @click="closeLastWinning">
      <view class="last-winning-display" :style="{ background: backgroundGradient }" @click.stop>
        <!-- 关闭按钮 -->
        <view class="close-btn" @click="closeLastWinning">
          <text class="close-icon">×</text>
        </view>
        <!-- 中奖动画效果 -->
        <view class="winning-animation">
          <view class="fireworks">
            <view class="firework" v-for="(style, index) in fireworkStyles" :key="index" :style="style"></view>
          </view>

          <!-- 中奖图标 -->
          <view class="winning-icon">
            <text class="icon-text">🎉</text>
          </view>

          <!-- 恭喜文字 -->
          <view class="congratulations">
            <text class="congrats-text">上次中奖记录</text>
          </view>

          <!-- 奖品信息 -->
          <view class="prize-info">
            <view class="prize-name">{{ testResult.prizeName }}</view>
            <view class="prize-desc">{{ testResult.prizeDesc }}</view>
          </view>

          <!-- 中奖时间 -->
          <view class="draw-time">
            <text>抽奖时间：{{ formatTime(testResult.drawTime) }}</text>
          </view>

          <!-- 领取说明 -->
          <view class="claim-instruction">
            <view class="instruction-title">领取说明</view>
            <view class="instruction-content">请联系商家微信客服领取奖品，出示此中奖记录即可。</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import WinningModal from '@/components/WinningModal/WinningModal.vue'

export default {
  components: {
    WinningModal
  },
  data() {
    return {
      winningModalVisible: false,
      lastWinningVisible: false,
      testResult: {
        prizeName: '一等奖',
        prizeDesc: '价值100元代金券',
        drawTime: new Date().toISOString(),
        isWinner: '1'
      },
      testMerchantConfig: {
        ui_config: JSON.stringify({
          primaryColor: '#667eea'
        })
      },
      testActivity: {
        activityName: '测试抽奖活动',
        claimInstruction: '请联系商家微信客服领取奖品，出示此中奖记录即可。'
      }
    }
  },
  computed: {
    backgroundGradient() {
      return 'linear-gradient(135deg, #667eea 0%, #4c63d2 100%)'
    },
    fireworkStyles() {
      const angles = [0, 60, 120, 180, 240, 300]
      return angles.map((angle, index) => ({
        transform: `rotate(${angle}deg)`,
        animationDelay: `${(index + 1) * 0.1}s`
      }))
    }
  },
  methods: {
    showWinningModal() {
      this.winningModalVisible = true
    },
    closeWinningModal() {
      this.winningModalVisible = false
    },
    showLastWinning() {
      this.lastWinningVisible = true
    },
    closeLastWinning() {
      this.lastWinningVisible = false
    },
    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #4c63d2 100%);
  padding: 40rpx;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  align-items: center;
}

.test-btn {
  width: 400rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 上次中奖记录全屏容器 */
.last-winning-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* 添加安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  /* 确保在所有设备上都能全屏显示 */
  width: 100vw;
  height: 100vh;
  /* 兼容性处理 */
  /* #ifdef H5 */
  width: 100%;
  height: 100%;
  /* #endif */
}

.last-winning-display {
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 20rpx;
  max-width: 650rpx;
  width: 95%;
  position: relative;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow-y: auto;
  /* #ifdef MP-WEIXIN */
  max-height: 85vh;
  /* #endif */
  /* #ifdef H5 */
  max-height: 90vh;
  /* #endif */
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.close-icon {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.winning-animation {
  text-align: center;
  position: relative;
}

.fireworks {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
}

.firework {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8rpx;
  height: 8rpx;
  background: #fff;
  border-radius: 50%;
  animation: fireworkAnimation 1.5s ease-out infinite;
  transform-origin: 0 0;
}

.winning-icon {
  margin: 40rpx 0 20rpx;
  animation: bounce 1s ease-in-out infinite;
}

.icon-text {
  font-size: 120rpx;
  line-height: 1;
}

.congratulations {
  margin-bottom: 30rpx;
}

.congrats-text {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.prize-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}

.prize-name {
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.prize-desc {
  color: #666;
  font-size: 28rpx;
}

.draw-time {
  margin: 20rpx 0 40rpx;
}

.draw-time text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.claim-instruction {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}

.instruction-title {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.instruction-content {
  color: #666;
  font-size: 26rpx;
  line-height: 1.5;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes fireworkAnimation {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
  }
}
</style>
